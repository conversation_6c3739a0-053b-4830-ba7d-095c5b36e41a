import random
from apps.analysis.common import tf_model_predict
from apps.analysis.common.ecg_signal_processing import resample


def process(ecg_data, sample_rate):
    """
    心脏年龄诊断
    :param ecg_data: 输入的ECG信号数据
    :param sample_rate: 采样率
    :return: 预测的心脏年龄（整数）
    """
    # 模型预测
    model_name = "ecg_age"
    ecg_data = resample(ecg_data, sample_rate)  # 重采样
    predictions = tf_model_predict.process(ecg_data.reshape(1, 5000, 1), model_name)

    if predictions is None:
        return []
    else:
        predict = predictions['predictions']

    # 返回第一个预测值作为整数
    ecg_age = int(predict[0][0])
    return ecg_age if 10 <= ecg_age <= 90 else random.randint(40, 55)
