import ast
import json
import time
import traceback

import numpy as np
from django.utils.decorators import method_decorator
from django.views import View

import apps.analysis.arrhythmia_diagnosis.diagnosis as arrhythmia_diagnosis_diagnosis
import apps.analysis.cad_cardiomyopathy.diagnosis as cad_cardiomyopathy_diagnosis
import apps.analysis.health_metrics.diagnosis as health_metrics_diagnosis
import apps.analysis.pqrstc.diagnosis as pqrstc_diagnosis
from apps.analysis import whitelist
from apps.analysis.common.custom_exception import BiosppyEcgError
from apps.analysis.common.data_filter import filter_negative_values
from apps.analysis.common.multiple_model.conclusion_diagnostic import ECGDiagnostic
from apps.analysis.diagnosis_filter.filter import apply_rules, FilterEntity
from apps.analysis.ecg_age import diagnosis as ecg_age_diagnosis
from apps.api.diagnose.business import save_api_log, get_diagnosis_details, get_diagnosis_result
from apps.models.analysis_models import AnalysisEntity, ArrhythmiaDiagnosisEntity, HealthMetricsEntity, MotionInfoEntity
from apps.signal_analysis.available import SignalAnalysis
from apps.signal_analysis.waveform import get_waveform, statistical_minute_wovefrom_indicators
from apps.utils.decorator import authentication
from apps.utils.get_response import GetResponse
from apps.utils.logger_helper import Logger
from apps.analysis.gravity.business import process as gravity_process


@method_decorator(authentication, name="dispatch")
class ArrhythmiaView(View):
    """
    心率失常诊断
    """

    def post(self, request, *args, **kwargs):
        """
        POST请求接口
        :param request: 请求对象
        :return: 诊断结果
        """
        custom_id = kwargs.get('custom_id')  # 默认给一个整数ID，避免错误
        ecg_data = None
        union_id = None

        param_list = ['signal', 'fs', 'adc_gain', 'adc_zero', 'union_id', 'ecg_age_key', 'health_metrics']

        try:
            data = json.loads(request.body)

            # 验证参数是否存在
            missing_params = [param for param in param_list if param not in data]

            if len(missing_params) > 0:
                return GetResponse.get_response(code=6)

            ecg_data_str = data['signal']  # ecg信号

            if isinstance(ecg_data_str, list):
                ecg_data = np.array(data['signal'])
            elif ecg_data_str.find('[') != -1 and ecg_data_str.find(']') != -1:
                ecg_data = np.array(ast.literal_eval(f"{ecg_data_str}"))
            else:
                ecg_data = np.array(ast.literal_eval(f"[{ecg_data_str}]"))  # 将心电信号转为nparray

            sampling_rate = data['fs']  # 采样率
            gain = data['adc_gain']  # 增益
            zero = data['adc_zero']  # 零点（基线）
            union_id = data['union_id']  # 用户ID
            ecg_age_key = data['ecg_age_key']  # 心脏年龄 0-不需要计算 1-需要计算
            health_metrics = data['health_metrics']  # 情绪指数 0-不需要计算 1-需要计算

            step_sec = data['step_sec'] if 'step_sec' in data else 1
            window_sec = data['window_sec'] if 'window_sec' in data else 5

            sampling_rate = int(sampling_rate)

            if len(ecg_data) < sampling_rate * 10:
                return GetResponse.get_response(code=6)

            if ecg_data is None or sampling_rate is None or gain is None or zero is None or gain <= 0:
                return GetResponse.get_response(code=5)

            if not isinstance(sampling_rate, int):
                return GetResponse.get_response(code=6)

            ecg_data = (ecg_data - zero) / gain  # 计算实际电压（检测电压-基线）/ 增益

            # 分析实体对象
            analysis_entity = AnalysisEntity()

            # 获取可用信号
            signal_analysis = SignalAnalysis(ecg_data, sampling_rate, window_sec=window_sec, step_sec=step_sec)
            normal_time_period = signal_analysis.get_available_signals()

            if len(normal_time_period) == 0:
                return GetResponse.get_response(code=10)

            analysis_entity.SignalQuantity = 1  # 信号质量 1-正常,-1-噪声

            arrhythmia_diagnosis, waveform_info_dict, diagnosis_info_dict = get_diagnosis_result(ecg_data, sampling_rate, normal_time_period)

            analysis_entity.DiagnosisTime = diagnosis_info_dict
            analysis_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis  # 设置诊断结果

            # 获取新增年龄
            if ecg_age_key == 1:
                default_signal_time_period = normal_time_period[0]  # 默认取第一个时间段
                default_ecg_data_processed = ecg_data[default_signal_time_period[0] * sampling_rate: default_signal_time_period[1] * sampling_rate]    # 默认有效信号段
                analysis_entity.ECGAge = ecg_age_diagnosis.process(default_ecg_data_processed, sampling_rate)  # 心脏年龄

            analysis_entity.HeartFailureRisk = 0  # 心衰风险（0-1）
            analysis_entity.VentricularFibrillationRisk = 0  # 室颤风险（0-1）
            analysis_entity.SyncopeRisk = 0  # 晕厥风险（0-1）
            analysis_entity.SleepStage = 0  # 睡眠阶段 取值范围为（0，1，2，3，4），对应睡眠阶段（Wake，N1， N2， N3, REM）
            analysis_entity.OSARisk = 0  # 阻塞性睡眠呼吸暂停风险，取值范围为（0-1）, -1代表时长小于两分钟
            analysis_entity.RespiratoryRate = 0  # 呼吸次数/min 取值范围[10-25]

            minute_waveform_info = statistical_minute_wovefrom_indicators(waveform_info_dict, sampling_rate)  # 统计一分钟内的指标

            analysis_entity.PQRSTC = pqrstc_diagnosis.process(minute_waveform_info)  # ECG信号指标

            analysis_entity.CADCardiomyopathy = cad_cardiomyopathy_diagnosis.process(minute_waveform_info)  # 心肌病冠心病诊断

            if health_metrics == 1:
                analysis_entity.HealthMetrics = health_metrics_diagnosis.process(minute_waveform_info)  # 健康指标

            analysis_entity.RRIntervals = ','.join(map(str, minute_waveform_info.get('waveform', {}).get('rr_intervals', [])))
            analysis_entity.NNIntervals = ','.join(map(str, minute_waveform_info.get('waveform', {}).get('nn_intervals', [])))

            # 处理加速度
            if 'gravity' in data and data['gravity']:

                start_time = time.time()
                gravity = data['gravity']
                analysis_entity.MotionInfo = gravity_process(union_id, data['gravity'])
                
                # 将运动强度为中(2)和高(3)的状态定义为噪音
                if analysis_entity.MotionInfo and analysis_entity.MotionInfo.motion_intensity in [2, 3]:
                    analysis_entity.SignalQuantity = -1


                end_time = time.time()
                print(f"5: {end_time - start_time} s")
            else:
                gravity = None
                analysis_entity.MotionInfo = MotionInfoEntity()

            # 诊断详情设置
            analysis_entity.DiagnosisDetails = get_diagnosis_details(analysis_entity, minute_waveform_info, sampling_rate)

            analysis_entity.ecg_id = save_api_log(ecg_data_str, gravity, sampling_rate, gain, zero, analysis_entity,
                                                  custom_id)  # ecg信号分析ID

            return GetResponse.get_response(code=0, data=self.after_process(analysis_entity))
        except BiosppyEcgError as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=7)
        except Exception as e:
            # 错误处理
            Logger().error(f'\n客户ID: {union_id}, 心电信号：{ecg_data}\n{traceback.format_exc()}')
            return GetResponse.get_response(code=2)

    def after_process(self, analysis_entity):
        """
        后处理
        :param analysis_entity: 分析实体对象
        :return: 处理后的分析实体对象
        """
        # 对诊断结果进行规则过滤
        filter_entity = FilterEntity()
        filter_entity.arrhythmia_diagnosis = [attr for attr, value in vars(analysis_entity.ArrhythmiaDiagnosis).items() if value == 1]
        filter_entity.qtc = analysis_entity.PQRSTC.QTc

        diagnosis_result = apply_rules(filter_entity, rule_type=2)

        # 创建一个实例
        arrhythmia_diagnosis = ArrhythmiaDiagnosisEntity()

        if len(diagnosis_result) > 0:
            # 将指定的属性设置为 1
            for attr in diagnosis_result:
                if hasattr(arrhythmia_diagnosis, attr):  # 检查属性是否存在
                    setattr(arrhythmia_diagnosis, attr, 1)

        analysis_entity.ArrhythmiaDiagnosis = arrhythmia_diagnosis

        if not any(value == 1 for value in vars(analysis_entity.ArrhythmiaDiagnosis).values()):
            analysis_entity.ArrhythmiaDiagnosis.SN = 1

        # 过滤负值并返回
        response_data = filter_negative_values(analysis_entity.to_entity_dict())

        return response_data
