import requests

from global_settings import tf_model


def process(data, model_name, version='v1'):
    """
    调用TF Serving模型
    :param data: 预测数据
    :param model_name: 模型名称
    :param version: 模型版本
    :return: 预测结果
    """
    tf_serving_url = tf_model['url']

    rest_api_url = f"{tf_serving_url}/{version}/models/{model_name}:predict"

    data = {
        "instances": data.tolist()  # 输入数据，确保是 4 维张量
    }

    # 发送 POST 请求
    response = requests.post(rest_api_url, json=data)

    # 检查响应
    if response.status_code == 200:
        return response.json()
    else:
        return None
