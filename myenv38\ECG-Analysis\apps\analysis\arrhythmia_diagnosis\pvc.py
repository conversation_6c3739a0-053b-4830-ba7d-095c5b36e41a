import numpy as np
from scipy.stats import pearsonr

BEAT_WINDOW_BEFORE_MS = 250
BEAT_WINDOW_AFTER_MS = 350
PREMATURITY_THRESHOLD = 0.85
COMPENSATION_THRESHOLD = 1.2
QRS_WIDTH_THRESHOLD_MS = 120
MORPHOLOGY_SIMILARITY_THRESHOLD = 0.8


def _get_beat_segment(signal, r_peak, sampling_rate):
    window_before = int(BEAT_WINDOW_BEFORE_MS / 1000 * sampling_rate)
    window_after = int(BEAT_WINDOW_AFTER_MS / 1000 * sampling_rate)
    start = r_peak - window_before
    end = r_peak + window_after
    if start < 0 or end > len(signal):
        return None
    return signal[start:end]


def _get_qrs_width_from_waveform_info(waveform_info, beat_index, sampling_rate):
    q_peaks = waveform_info.get('q_peaks')
    s_peaks = waveform_info.get('s_peaks')
    if q_peaks is None or s_peaks is None or beat_index >= len(q_peaks) or beat_index >= len(s_peaks):
        return None

    q_idx = q_peaks[beat_index]
    s_idx = s_peaks[beat_index]

    if q_idx > 0 and s_idx > 0 and s_idx > q_idx:
        return (s_idx - q_idx) * 1000 / sampling_rate
    return None


def _create_templates(signals, r_peaks, rr_intervals, median_rr, sampling_rate):
    templates = {}
    normal_beat_segments = {lead: [] for lead in signals}

    # 寻找最多5个稳定的正常窦性心拍来创建模板
    for i in range(1, len(r_peaks) - 1):
        # 正常心拍的标准: 前后RR间期都接近中位值
        if 0.9 * median_rr <= rr_intervals[i - 1] <= 1.1 * median_rr and \
                0.9 * median_rr <= rr_intervals[i] <= 1.1 * median_rr:

            is_valid_beat = True
            beat_segments = {}
            for lead_name, signal in signals.items():
                segment = _get_beat_segment(signal, r_peaks[i], sampling_rate)
                if segment is None:
                    is_valid_beat = False
                    break
                beat_segments[lead_name] = segment

            if is_valid_beat:
                for lead_name, segment in beat_segments.items():
                    normal_beat_segments[lead_name].append(segment)

            # 找到5个样本后即可停止
            if len(next(iter(normal_beat_segments.values()))) >= 5:
                break

    # 对收集到的正常心拍进行平均，形成最终模板
    for lead_name, segments in normal_beat_segments.items():
        if segments:
            templates[lead_name] = np.mean(segments, axis=0)

    return templates


def _get_qrs_polarity(beat_segment, sampling_rate):
    center_index = len(beat_segment) // 2
    qrs_window = int(80 / 1000 * sampling_rate / 2)

    start = max(0, center_index - qrs_window)
    end = min(len(beat_segment), center_index + qrs_window)

    qrs_segment = beat_segment[start:end]

    if len(qrs_segment) == 0:
        return 0

    max_val = np.max(qrs_segment)
    min_val = np.min(qrs_segment)

    if max_val > abs(min_val):
        return 1
    else:
        return -1


def process(signals, sampling_rate, waveform_info):
    # --- 1. 输入验证与策略选择 ---
    if not isinstance(signals, dict) or not signals:
        return False

    # 确定主导联用于提取基础波形信息
    primary_lead_name = 'lead2' if 'lead2' in signals else next(iter(signals))

    waveform = waveform_info.get('waveform', {})
    r_peaks = waveform.get('r_peaks')
    rr_intervals = waveform.get('rr_intervals')

    # 检查是否有足够的心拍进行分析
    if r_peaks is None or len(r_peaks) < 3 or rr_intervals is None or len(rr_intervals) < 2:
        return False

    # 根据输入信号的导联数，自动选择模式
    mode = 'dual_lead' if 'lead1' in signals and 'lead2' in signals else 'single_lead'

    # --- 2. 创建正常心拍模板 ---
    median_rr = np.median(rr_intervals)
    if median_rr == 0: return False

    templates = _create_templates(signals, r_peaks, rr_intervals, median_rr, sampling_rate)

    if not templates:
        return False

    # --- 3. 逐个心拍分析与分类 ---
    pvc_count = 0
    for i in range(len(r_peaks)):
        # a) 时间特征分析
        is_premature = False
        has_compensatory_pause = False
        if i > 0:
            is_premature = rr_intervals[i - 1] < PREMATURITY_THRESHOLD * median_rr
        if i < len(rr_intervals):
            has_compensatory_pause = rr_intervals[i] > COMPENSATION_THRESHOLD * median_rr

        if not is_premature:
            continue

        # b) 形态学与心电轴特征分析
        is_wide_qrs = False
        is_morph_abnormal = False
        has_axis_shift = False

        qrs_width = _get_qrs_width_from_waveform_info(waveform, i, sampling_rate)
        if qrs_width is not None and qrs_width > QRS_WIDTH_THRESHOLD_MS:
            is_wide_qrs = True

        beat_segments = {}
        for lead_name, signal in signals.items():
            segment = _get_beat_segment(signal, r_peaks[i], sampling_rate)
            if segment is not None:
                beat_segments[lead_name] = segment

        if not beat_segments:
            continue

        # 检查所有可用导联的形态相似性
        for lead_name, segment in beat_segments.items():
            if lead_name in templates:
                try:
                    similarity, _ = pearsonr(segment, templates[lead_name])
                    if similarity < MORPHOLOGY_SIMILARITY_THRESHOLD:
                        is_morph_abnormal = True
                        break
                except:
                    continue

        # 如果是双导联模式，检查心电轴偏移
        if mode == 'dual_lead' and 'lead1' in beat_segments and 'lead2' in beat_segments and \
                'lead1' in templates and 'lead2' in templates:

            normal_polarity_l1 = _get_qrs_polarity(templates['lead1'], sampling_rate)
            normal_polarity_l2 = _get_qrs_polarity(templates['lead2'], sampling_rate)

            current_polarity_l1 = _get_qrs_polarity(beat_segments['lead1'], sampling_rate)
            current_polarity_l2 = _get_qrs_polarity(beat_segments['lead2'], sampling_rate)

            # 简单的轴偏移判断：任一导联的主方向翻转
            if normal_polarity_l1 != 0 and current_polarity_l1 != 0 and normal_polarity_l1 != current_polarity_l1:
                has_axis_shift = True
            if not has_axis_shift and normal_polarity_l2 != 0 and current_polarity_l2 != 0 and normal_polarity_l2 != current_polarity_l2:
                has_axis_shift = True

        is_pvc = False
        if mode == 'single_lead':
            # 单导联规则: 早搏 + 代偿间歇 + (QRS增宽 或 形态异常)
            if has_compensatory_pause and (is_wide_qrs or is_morph_abnormal):
                is_pvc = True
        else:  # 双导联模式
            # 强证据: 早搏 + QRS增宽 + 形态异常 + 心电轴偏移
            if is_wide_qrs and is_morph_abnormal and has_axis_shift:
                is_pvc = True
            # 可靠证据: 早搏 + 代偿间歇 + (QRS增宽 或 (形态异常且心电轴偏移))
            elif has_compensatory_pause and (is_wide_qrs or (is_morph_abnormal and has_axis_shift)):
                is_pvc = True

        if is_pvc:
            pvc_count += 1
    # --- 4. 最终诊断 ---
    final_decision = pvc_count > 0  # 任何检测到的PVC都将触发阳性结果

    return final_decision
