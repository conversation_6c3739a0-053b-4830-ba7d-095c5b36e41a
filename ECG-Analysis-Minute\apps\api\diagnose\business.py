import time

from apps.analysis.arrhythmia_diagnosis.diagnosis import method_diagnosis, llm_diagnosis, apply_mutually_exclusive_rules
from apps.analysis.arrhythmia_diagnosis.diagnosis_detail.af_detail import quantify_af, detect_af_episodes
from apps.analysis.arrhythmia_diagnosis.diagnosis_detail.pvc_detail import quantify_pvc, detect_classify_pvc
from apps.analysis.common.multiple_model.conclusion_diagnostic import ECGDiagnostic
from apps.analysis.diagnosis_filter.filter import apply_rules, FilterEntity
from apps.models.Interface_log_models import InterfaceLogEntity, RequestParam
from apps.models.analysis_models import DiagnosisDetailEntity, ArrhythmiaDiagnosisEntity, AnalysisEntity
from apps.signal_analysis.waveform import get_waveform
from apps.utils import api_analysis_log


def save_api_log(ecg_data_str, gravity, sampling_rate, gain, zero, analysis_entity, custom_id):
    """
    记录接口日志
    :param ecg_data_str: 心电数据字符串
    :param gravity: 重力数据
    :param sampling_rate: 采样率
    :param gain: 增益
    :param zero: 基线
    :param analysis_entity: 接口分析内容
    :param custom_id: 客户ID
    :return:
    """
    # 设置日志实体
    interface_log = InterfaceLogEntity()

    # 设置请求参数
    request_param = RequestParam()
    request_param.ecg_data = ecg_data_str
    request_param.gravity = gravity
    request_param.fs = sampling_rate
    request_param.gain = gain
    request_param.zero = zero

    interface_log.RequestParam = request_param.to_dict()

    # 设置响应参数
    interface_log.ResponseParam = analysis_entity.to_entity_dict()

    # 写入日志
    ecg_id = api_analysis_log.record(custom_id, interface_log)

    return ecg_id


def get_diagnosis_details(analysis_entity, waveform_info, sampling_rate):
    """
    获取诊断详情
    :param analysis_entity:
    :param waveform_info:
    :param sampling_rate:
    :return:
    """
    waveform = waveform_info['waveform']
    diagnosis_details = []

    if analysis_entity.ArrhythmiaDiagnosis.AF == 1:
        rr_intervals = waveform.get('rr_intervals')
        r_peaks = waveform.get('r_peaks')
        if rr_intervals is not None and r_peaks is not None:
            if len(rr_intervals) > 0:
                total_duration_sec = float(sum(rr_intervals))
            elif len(r_peaks) > 1:
                total_duration_sec = float(r_peaks[-1]) / sampling_rate
            else:
                total_duration_sec = 10.0
            af_episodes = detect_af_episodes(rr_intervals, r_peaks, sampling_rate)
            quantification = quantify_af(af_episodes, total_duration_sec)
            diagnosis_detail = DiagnosisDetailEntity()
            diagnosis_detail.disease_code = 'AF'  # 疾病编码
            diagnosis_detail.total_episodes = quantification['total_episodes']  # 总阵数
            diagnosis_detail.total_duration = quantification['total_duration']  # 总时长(秒)
            diagnosis_detail.fastest_hr = quantification['fastest_hr']  # 最快心率(次 / 分钟)
            diagnosis_detail.longest_duration = quantification['longest_episode_duration']  # 最长持续(秒)
            diagnosis_detail.longest_rr = quantification['longest_rr_during_af']  # 最长RR(秒)

            diagnosis_details.append(diagnosis_detail.to_dict())


    if analysis_entity.ArrhythmiaDiagnosis.PVC == 1:
        rr_intervals = waveform.get('rr_intervals')
        r_peaks = waveform.get('r_peaks')
        if rr_intervals is not None and r_peaks is not None:
            quantification = quantify_pvc(detect_classify_pvc(rr_intervals, r_peaks), len(r_peaks))
            diagnosis_detail = DiagnosisDetailEntity()
            diagnosis_detail.disease_code = 'PVC'
            diagnosis_detail.single_count = quantification['counts_by_type']['single']  # 单发个数
            diagnosis_detail.pair_count = quantification['counts_by_type']['pair']  # 成对阵数
            diagnosis_detail.bigeminy_count = quantification['counts_by_type']['bigeminy_count']  # 二联律次数
            diagnosis_detail.trigeminy_count = quantification['counts_by_type']['trigeminy_count']  # 三联律次数
            diagnosis_detail.run_count = len(quantification['counts_by_type']['run'])  # 连发阵数
            diagnosis_detail.max_consecutive = quantification['max_consecutive_pvc']  # 连发最多个数
            diagnosis_detail.fastest_run_hr = quantification['fastest_vt_hr']  # 连发最快心率(次 / 分钟)
            diagnosis_detail.slowest_run_hr = quantification['slowest_vt_hr']  # 连发最慢心率(次 / 分钟)
            diagnosis_detail.hr_total_count = quantification['total_pvc_count']  # 心搏总数

            diagnosis_details.append(diagnosis_detail.to_dict())


    return diagnosis_details


def get_diagnosis_result(ecg_data, sampling_rate, normal_time_period):
    """
    获取诊断结果
    :param ecg_data: 原始 ECG 信号数据
    :param sampling_rate: 采样率
    :param normal_time_period: 正常时间区间
    :return:
    """
    arrhythmia_diagnosis = ArrhythmiaDiagnosisEntity()

    diagnosis_result_list = []

    ecg_diagnostic = ECGDiagnostic(sampling_rate)

    waveform_info_dict = {}

    diagnosis_info_dict = {}

    for i, time_period in enumerate(normal_time_period):
        # 获取时间区间
        start_time = time_period[0]
        end_time = time_period[1]
        # 获取时间区间内的ECG数据
        segment = ecg_data[start_time * sampling_rate: end_time * sampling_rate]

        waveform_info = get_waveform(segment, sampling_rate)  # 波形分析
        waveform_info_dict[start_time] = waveform_info  # 保存波形分析结果

        method_diag_dict = method_diagnosis(segment, sampling_rate, waveform_info)  # 方法诊断

        diagnosis_result = None

        if any(method_diag_dict.values()):
            # 对诊断结果进行规则过滤
            filter_entity = FilterEntity()
            filter_entity.arrhythmia_diagnosis = [key for key, value in method_diag_dict.items() if value]
            filter_entity.hr = waveform_info['heart_rate']['hr']

            diagnosis_result = apply_rules(filter_entity, rule_type=1)  # 规则过滤
            diagnosis_result_list += diagnosis_result

        diagnosis_info_dict[f'{start_time}-{end_time}'] = ', '.join(diagnosis_result) if diagnosis_result else None

    for key, value in diagnosis_info_dict.items():
        if value is None:
            time_period = key.split('-')
            start_time = int(time_period[0])
            end_time = int(time_period[1])
            segment = ecg_data[start_time * sampling_rate: end_time * sampling_rate]

            conclusion = ecg_diagnostic.diagnose([segment])[0]

            if len(conclusion) > 0:
                diagnosis_info_dict[key] = ', '.join(conclusion)
                diagnosis_result_list += conclusion
            else:
                waveform_info = waveform_info_dict[start_time]

                result_llm = llm_diagnosis(waveform_info, sampling_rate)

                if result_llm:
                    llm_result = [k for k, v in result_llm.items() if v]
                    diagnosis_info_dict[key] = ', '.join(llm_result)
                    diagnosis_result_list.append(llm_result)

    # 去重并排除 [] 和 None
    diagnostic_results = list({item for item in diagnosis_result_list if item and item is not None})

    # 症状互斥处理
    if diagnostic_results:
        final_labels = apply_mutually_exclusive_rules(diagnostic_results)

        if len(final_labels) == 0:
            arrhythmia_diagnosis.SN = 1
        else:
            for final_label in final_labels:
                setattr(arrhythmia_diagnosis, final_label, 1)
    else:
        arrhythmia_diagnosis.SN = 1

    return arrhythmia_diagnosis, waveform_info_dict, diagnosis_info_dict

