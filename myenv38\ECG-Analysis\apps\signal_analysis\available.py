import numpy as np
from biosppy.signals import ecg
from scipy import signal
from scipy.fft import fft, fftfreq

from apps.signal_analysis.filtering import wavelet_filter


class SignalAnalysis(object):
    """
    可穿戴设备ECG噪声检测
    """
    _window_sec = 5  # 滑动窗口的时长
    _max_hr = 300   # 最大心率阈值
    _filter_raw_signal = None
    _sample_rate = None
    _amp_range_max = 5
    _amp_range_min = 0.3
    _zcr_min = 0.75
    _hf_energy_max = 0.35
    _baseline_drift_max = 0.55
    _ptp_max = 0.4

    def __init__(self, ecg_data, sampling_rate):
        """
        初始化
        :param ecg_data: 原始ECG信号（一维数组）。
        :param sampling_rate: 采样率
        """
        filter_raw_signal = wavelet_filter(ecg_data, sampling_rate)  # 心电信号滤波处理
        # 使用 numpy.isnan() 找出非 NaN 的元素
        non_nan_signal = ~np.isnan(filter_raw_signal)

        self._filter_raw_signal = filter_raw_signal[non_nan_signal]

        self._sample_rate = sampling_rate

    def get_available_signals(self):
        """
        可穿戴设备ECG噪声检测核心函数
        :return 返回非噪音时间段，格式为[[start_time, end_time], ...]。
        """
        if self._filter_raw_signal is None:
            return -3, []

        # 初始化参数
        signal_quality = 1  # 信号质量

        # 计算窗口大小和步长
        window_size = int(self._window_sec * self._sample_rate)

        num_samples = len(self._filter_raw_signal)

        normal_time_period = []

        range_start = 0
        range_end = num_samples - window_size + 1

        # 滑动窗口处理
        for index, start in enumerate(range(range_start, range_end, self._sample_rate)):
            end = start + window_size

            # 计算当前窗口的时间范围
            start_time = int(start / self._sample_rate)
            end_time = int(start_time + self._window_sec)

            segment = self._filter_raw_signal[start:end]

            baseline_drift = self.baseline_variation(segment)
            ptp = np.std(segment)
            zcr = self.zero_cross_rate(segment)

            # 特征提取
            features = {
                'amp_range': np.ptp(segment),  # 幅度范围
                'zcr': zcr,  # 过零率
                'hf_energy': self.high_freq_energy(segment),
                'baseline_drift': baseline_drift,
                'ptp': ptp
            }

            if not self.is_noisy_window(features):
                normal_time_period.append([start_time, end_time])

        if len(normal_time_period) == 0:
            return self.get_noisy_type(self._filter_raw_signal[0: 10 * self._sample_rate]), [[0, 10]]
        else:
            normal_time_period = self.get_continuous_normal_time(normal_time_period)

            if len(normal_time_period) == 0:
                return self.get_noisy_type(self._filter_raw_signal[0: 10 * self._sample_rate]), [[0, 10]]
            else:
                filtered_normal_time_period = []

                for index, time_period in enumerate(normal_time_period):
                    start_time = time_period[0]
                    end_time = time_period[1]
                    segment = self._filter_raw_signal[start_time * self._sample_rate: end_time * self._sample_rate]

                    # 预计算整段信号的心率
                    try:
                        ecg_info = ecg.ecg(signal=segment, sampling_rate=self._sample_rate, show=False)
                        rpeaks = ecg_info['rpeaks']  # 获取R波峰值的索引

                        if self.calculate_qrs_width(ecg_info):
                            # QRS波群宽度检查通过，进行进一步的心率范围检查

                            # 计算相邻R波之间的时间间隔（RR间期），然后转换为心率（bpm）
                            rr_intervals = np.diff(rpeaks) / self._sample_rate

                            if np.max(ecg_info['heart_rate']) <= self._max_hr:
                                # 心率检查通过检查RR变异系数
                                rr_mean = np.mean(rr_intervals)
                                rr_std = np.std(rr_intervals)
                                rr_cv = float(rr_std / rr_mean) if rr_mean > 0 else 0.0

                                if rr_cv <= 0.3:
                                    filtered_normal_time_period.append(time_period)
                    except Exception:
                        pass

                if len(filtered_normal_time_period) == 0:
                    return self.get_noisy_type(self._filter_raw_signal[0: 10 * self._sample_rate]), [[0, 10]]
                else:
                    return signal_quality, filtered_normal_time_period

    def calculate_qrs_width(self, ecg_info):
        """
        计算QRS波群的宽度
        :param ecg_info: BioSPPy ECG分析结果
        :return: 是否符合QRS波群的宽度要求
        """
        # 定义QRS波群的宽度范围（通常为0.06秒到0.12秒）
        min_width = 0.04
        max_width = 0.3

        q_positions = ecg.getQPositions(ecg_info)
        q_start_positions = q_positions['Q_start_positions']

        s_positions = ecg.getSPositions(ecg_info)
        s_end_positions = s_positions["S_end_positions"]

        for q_start in q_start_positions:
            # 找到最近的 S 波结束位置
            s_end = self.find_nearest_s_end(q_start, s_end_positions)
            if s_end is not None:
                # 计算 QRS 波群的宽度
                qrs_width = (s_end - q_start) / self._sample_rate

                if qrs_width < min_width or qrs_width > max_width:
                    return False
            else:
                return False

        return True

    def find_nearest_s_end(self, q_start, s_end_positions):
        """
        找到最近的 S 波结束位置。
        :param q_start: Q 波的起始位置
        :param s_end_positions: S 波的结束位置数组
        :return: 最近的 S 波结束位置，如果未找到则返回 None
        """
        # 过滤掉小于 Q 波起始位置的 S 波结束位置
        valid_s_end_positions = [s for s in s_end_positions if s >= q_start]

        if len(valid_s_end_positions) == 0:
            return None  # 如果没有有效的 S 波结束位置，则返回 None

        # 计算 Q 波起始位置和所有有效 S 波结束位置之间的距离
        distances = np.abs(valid_s_end_positions - q_start)
        # 找到最小距离
        min_distance = np.min(distances)
        # 如果最小距离在合理范围内（例如，小于 0.2 秒），则认为找到了匹配的 S 波
        if min_distance < 0.5 * self._sample_rate:
            nearest_s_end = valid_s_end_positions[np.argmin(distances)]
            return nearest_s_end
        else:
            return None

    def match_q_s_waves(self, q_starts, s_end_positions):
        """
        匹配 Q 波和 S 波。
        :param q_starts: Q 波的起始位置数组
        :param s_end_positions: S 波的结束位置数组
        :return: 匹配结果列表，包含匹配的 Q 波和 S 波位置，未匹配的 Q 波位置也会被记录
        """
        matched_pairs = []
        unmatched_q_starts = []

        for q_start in q_starts:
            nearest_s_end = self.find_nearest_s_end(q_start, s_end_positions)
            if nearest_s_end is not None:
                matched_pairs.append((q_start, nearest_s_end))
            else:
                unmatched_q_starts.append(q_start)

        return matched_pairs, unmatched_q_starts

    def get_noisy_type(self, segment):
        """
        识别噪声类型
        :param segment: 噪声窗口
        :return: 噪声类型
        """
        signal_quality = -1

        amp_range = np.ptp(segment)
        zcr = self.zero_cross_rate(segment)
        hf_energy = self.high_freq_energy(segment)

        # 检查是否为平直线+高频振荡噪声
        if amp_range < self._amp_range_min or zcr < self._zcr_min:
            signal_quality = -2
        elif hf_energy> self._hf_energy_max or amp_range > self._amp_range_max:
            signal_quality = -3

        return signal_quality


    def get_continuous_normal_time(self, intervals):
        # 找出最小和最大时间
        min_t = min(start for start, _ in intervals)
        max_t = max(end for _, end in intervals)

        # 创建时间线数组
        timeline = [0] * (max_t - min_t + 1)

        # 标记覆盖的时间点
        for start, end in intervals:
            for t in range(start - min_t, end - min_t + 1):
                if 0 <= t < len(timeline):
                    timeline[t] = 1

        # 找出所有连续10秒时间段
        result = []
        for start_idx in range(len(timeline) - 10):
            if all(timeline[start_idx + i] for i in range(11)):
                real_start = start_idx + min_t
                result.append([real_start, real_start + 10])

        return result

    def zero_cross_rate(self, sig, noise_threshold_multiplier=1e-3, flat_threshold=5e-4,
                        min_voltage_threshold=0.05):
        """
        优化过零率计算，忽略微小波动，并根据采样率归一化结果。

        参数:
        - sig: 输入信号
        - sample_rate: 采样率
        - noise_threshold_multiplier: 噪声阈值乘性因子，默认为1e-3
        - flat_threshold: 平直线段检测阈值，默认为5e-4
        - min_voltage_threshold: 最小电压阈值，默认为0.05

        返回:
        - 每秒的过零率
        """
        # 1. 计算信号动态范围
        abs_max = np.max(np.abs(sig))

        # 2. 自适应噪声阈值（基于信号量级）
        noise_threshold = max(1e-6, abs_max * noise_threshold_multiplier)

        # 3. 平直线段检测条件
        is_flat = (abs_max < flat_threshold)  # 经验阈值，可根据ECG设备调整

        if is_flat:
            return 0.0  # 如果是平直信号，直接返回ZCR为0

        # 4. 真正的过零检测
        zero_crossings = 0
        for i in range(1, len(sig)):
            # 双条件检测：符号变化 + 跨越幅度阈值 + 最小电压阈值
            if (sig[i - 1] * sig[i] <= 0) and \
                    (abs(sig[i - 1]) > noise_threshold or abs(sig[i]) > noise_threshold) and \
                    (abs(sig[i - 1]) >= min_voltage_threshold or abs(sig[i]) >= min_voltage_threshold):
                zero_crossings += 1

        # 5. 计算过零率（每秒）
        zcr_per_second = zero_crossings * self._sample_rate / (len(sig) - 1)

        return zcr_per_second


    def high_freq_energy(self, sig, cutoff=25):
        """
        可穿戴设备特化高频能量检测。

        参数:
        - sig: 输入信号
        - cutoff: 高频截止频率，默认为25

        返回:
        - 高频能量占比
        """
        if len(sig) == 0:
            return 0.0

        # 去除直流分量
        sig_centered = sig - np.mean(sig)

        # 计算FFT
        fft_vals = np.abs(fft(sig_centered))

        # 计算频率
        freqs = np.abs(fftfreq(len(sig), 1 / self._sample_rate))

        # 高频掩码
        max_freq = min(self._sample_rate // 2, 100)
        hf_mask = (freqs > cutoff) & (freqs < max_freq)

        # 计算高频能量占比
        total_energy = np.sum(fft_vals)
        hf_energy = np.sum(fft_vals[hf_mask]) / (total_energy + 1e-6)

        return hf_energy

    def baseline_variation(self, sig, cutoff=0.5):
        """
        量化基线漂移（低频成分）

        参数:
        - sig: 输入信号
        - cutoff: 低通滤波器截止频率 (Hz)，默认为0.5Hz

        返回:
        - dict: 包含基线漂移的多个量化指标
        """
        if len(sig) < 10:  # 至少需要10个采样点
            return 0

        # 设计低通滤波器提取基线
        b, a = signal.butter(3, cutoff, btype='lowpass', fs=self._sample_rate)

        # 应用零相位滤波
        baseline = signal.filtfilt(b, a, sig)

        # 计算各种指标
        return np.std(baseline)  # 标准差


    def is_noisy_window(self, features):
        """动态阈值噪声判断（基于可穿戴设备经验值）"""
        rules = [
            features['amp_range'] < self._amp_range_min,  # 信号丢失（<20μV）
            features['amp_range'] > self._amp_range_max,
            features['zcr'] < self._zcr_min,
            features['hf_energy'] > self._hf_energy_max,  # 肌电干扰阈值
            features['baseline_drift'] > self._baseline_drift_max,  # 基线漂移阈值(mV)
            features['ptp'] > self._ptp_max,  # 峰-峰值阈值(mV)
        ]

        # 检查是否有规则被触发
        return any(rules)


